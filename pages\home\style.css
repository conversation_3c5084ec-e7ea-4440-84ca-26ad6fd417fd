/* style.css for home page */
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  background: #e0f7fa;
}

header {
  background: #00796b;
  color: #fff;
  padding: 1.5rem 0;
  text-align: center;
}

nav {
  background: #004d40;
  padding: 0.5rem 0;
  text-align: center;
}

nav a {
  color: #fff;
  text-decoration: none;
  margin: 0 1.5rem;
  font-weight: bold;
  transition: color 0.2s;
}

nav a:hover {
  color: #b2dfdb;
}

.hero {
  background: url("https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1200&q=80")
    center/cover no-repeat;
  color: #fff;
  padding: 5rem 2rem;
  text-align: center;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 8px #0008;
}

.hero p {
  font-size: 1.5rem;
  text-shadow: 1px 1px 6px #0007;
}

.content {
  max-width: 900px;
  margin: 2rem auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px #0001;
  padding: 2rem;
}

.features {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: space-between;
}

.feature {
  flex: 1 1 250px;
  background: #b2dfdb;
  border-radius: 6px;
  padding: 1.5rem;
  text-align: center;
}

footer {
  background: #004d40;
  color: #fff;
  text-align: center;
  padding: 1rem 0;
  margin-top: 2rem;
}
